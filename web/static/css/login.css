/* ===================================
   Apple Minimal Login Page
   苹果极简风格登录页面
   =================================== */

/* Clean Professional Design System */
:root {
  /* Primary Colors - 专业蓝色系 */
  --primary: #2563EB;
  --primary-hover: #1D4ED8;
  --primary-active: #1E40AF;
  --primary-light: #DBEAFE;

  /* Neutral Colors - 现代灰色系 */
  --white: #FFFFFF;
  --gray-50: #F8FAFC;
  --gray-100: #F1F5F9;
  --gray-200: #E2E8F0;
  --gray-300: #CBD5E1;
  --gray-400: #94A3B8;
  --gray-500: #64748B;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1E293B;
  --gray-900: #0F172A;

  /* Text Colors */
  --text-primary: #1E293B;
  --text-secondary: #475569;
  --text-tertiary: #64748B;
  --text-muted: #94A3B8;

  /* Background Colors */
  --bg-primary: #FFFFFF;
  --bg-secondary: #F8FAFC;
  --bg-tertiary: #F1F5F9;

  /* Border Colors */
  --border-light: #F1F5F9;
  --border-medium: #E2E8F0;
  --border-strong: #CBD5E1;

  /* Status Colors */
  --success: #059669;
  --success-bg: #ECFDF5;
  --error: #DC2626;
  --error-bg: #FEF2F2;
  --warning: #D97706;
  --warning-bg: #FFFBEB;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);

  /* Border Radius */
  --radius-sm: 6px;
  --radius: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 20px;

  /* Spacing */
  --space-1: 0.25rem;  /* 4px */
  --space-2: 0.5rem;   /* 8px */
  --space-3: 0.75rem;  /* 12px */
  --space-4: 1rem;     /* 16px */
  --space-5: 1.25rem;  /* 20px */
  --space-6: 1.5rem;   /* 24px */
  --space-8: 2rem;     /* 32px */
  --space-10: 2.5rem;  /* 40px */
  --space-12: 3rem;    /* 48px */

  /* Typography */
  --text-xs: 0.75rem;   /* 12px */
  --text-sm: 0.875rem;  /* 14px */
  --text-base: 1rem;    /* 16px */
  --text-lg: 1.125rem;  /* 18px */
  --text-xl: 1.25rem;   /* 20px */
  --text-2xl: 1.5rem;   /* 24px */
  --text-3xl: 1.875rem; /* 30px */

  /* Font Weights */
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;

  /* Transitions */
  --transition: all 0.15s ease-out;
  --transition-slow: all 0.3s ease-out;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  :root {
    --white: #0F172A;
    --bg-primary: #1E293B;
    --bg-secondary: #0F172A;
    --bg-tertiary: #334155;

    --text-primary: #F8FAFC;
    --text-secondary: #E2E8F0;
    --text-tertiary: #CBD5E1;
    --text-muted: #94A3B8;

    --border-light: #334155;
    --border-medium: #475569;
    --border-strong: #64748B;

    --gray-100: #334155;
    --gray-200: #475569;

    --primary: #3B82F6;
    --primary-hover: #60A5FA;
    --primary-active: #2563EB;
  }
}

/* ===================================
   Apple基础样式
   =================================== */

/* 全局样式 */
* {
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Inter', 'Segoe UI', 'Roboto', sans-serif;
  background: var(--bg-secondary);
  color: var(--text-primary);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
  padding: 0;
  font-size: var(--text-base);
  min-height: 100vh;
}

/* 简洁背景 */
.apple-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--bg-secondary);
  z-index: -1;
}

/* 登录容器 */
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-6);
}

.login-wrapper {
  width: 100%;
  max-width: 400px;
}

/* ===================================
   Apple卡片样式
   =================================== */

.apple-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  transition: var(--transition);
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  position: relative;
}

.apple-card:hover {
  box-shadow: var(--shadow-xl);
}

/* ===================================
   Apple头部样式
   =================================== */

.apple-header {
  padding: var(--space-8) var(--space-6) var(--space-6);
  text-align: center;
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-light);
}

.apple-logo {
  margin-bottom: var(--space-5);
}

.apple-logo svg {
  width: 32px;
  height: 32px;
  color: var(--primary);
}

.apple-title {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin: 0 0 var(--space-2) 0;
  letter-spacing: -0.025em;
}

.apple-subtitle {
  font-size: var(--text-base);
  color: var(--text-secondary);
  font-weight: var(--font-normal);
  margin: 0;
}

/* ===================================
   主体样式
   =================================== */

.apple-body {
  padding: var(--space-6);
}

/* ===================================
   Apple表单组件
   =================================== */

.apple-field {
  margin-bottom: var(--space-5);
}

.apple-label {
  display: block;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.apple-input {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  font-size: var(--text-base);
  font-family: inherit;
  color: var(--text-primary);
  background: var(--bg-primary);
  border: 1px solid var(--border-medium);
  border-radius: var(--radius);
  outline: none;
  transition: var(--transition);
  -webkit-appearance: none;
  appearance: none;
  min-height: 44px;
}

.apple-input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.apple-input::placeholder {
  color: var(--text-muted);
}

.apple-input-wrapper {
  position: relative;
}

.apple-input-action {
  position: absolute;
  right: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius-sm);
  transition: var(--transition);
}

.apple-input-action:hover {
  color: var(--text-secondary);
  background: var(--gray-100);
}

/* 复选框 */
.apple-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
}

.apple-checkbox-input {
  width: 16px;
  height: 16px;
  margin-right: var(--space-2);
  accent-color: var(--primary);
  cursor: pointer;
}

.apple-checkbox-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  cursor: pointer;
}

/* 选项区域 */
.apple-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
}

.apple-link {
  color: var(--primary);
  text-decoration: none;
  font-size: var(--text-sm);
  transition: var(--transition);
}

.apple-link:hover {
  color: var(--primary-hover);
  text-decoration: underline;
}

/* ===================================
   Apple按钮组件
   =================================== */

.apple-button-group {
  margin-bottom: var(--space-5);
}

.apple-button {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: none;
  border-radius: var(--radius);
  font-family: inherit;
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  cursor: pointer;
  outline: none;
  transition: var(--transition);
  user-select: none;
  min-height: 44px;
}

.apple-button-primary {
  background: var(--primary);
  color: white;
  box-shadow: var(--shadow-sm);
}

.apple-button-primary:hover:not(:disabled) {
  background: var(--primary-hover);
  box-shadow: var(--shadow);
}

.apple-button-primary:active {
  background: var(--primary-active);
  transform: scale(0.98);
}

.apple-button:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
}

.button-loading {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: var(--transition);
}

.apple-button.loading .button-content {
  opacity: 0;
}

.apple-button.loading .button-loading {
  opacity: 1;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: var(--space-2);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ===================================
   Apple警告框
   =================================== */

.apple-alert {
  padding: var(--space-3);
  border-radius: var(--radius);
  margin-bottom: var(--space-4);
  font-size: var(--text-sm);
  text-align: center;
  font-weight: var(--font-medium);
}

.apple-alert-error {
  background: var(--error-bg);
  color: var(--error);
  border: 1px solid rgba(220, 38, 38, 0.2);
}

.apple-alert-info {
  background: rgba(37, 99, 235, 0.05);
  color: var(--primary);
  border: 1px solid rgba(37, 99, 235, 0.2);
}

/* ===================================
   Apple页脚
   =================================== */

.apple-footer {
  margin-top: var(--space-8);
  text-align: center;
  padding: var(--space-4) 0;
}

.footer-links {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--space-4);
  margin-bottom: var(--space-3);
}

.footer-link {
  color: var(--primary);
  text-decoration: none;
  font-size: var(--text-xs);
  transition: var(--transition);
}

.footer-link:hover {
  color: var(--primary-hover);
  text-decoration: underline;
}

.apple-copyright {
  text-align: center;
}

.apple-copyright span {
  font-size: var(--text-xs);
  color: var(--text-muted);
}

/* ===================================
   Apple表单验证
   =================================== */

.apple-input.is-invalid {
  border-color: var(--error);
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.apple-input.is-valid {
  border-color: var(--success);
  box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
}

/* ===================================
   Apple响应式设计
   =================================== */

/* 响应式设计 */

/* 超小屏幕 (手机竖屏) */
@media (max-width: 480px) {
  .login-container {
    padding: var(--space-4);
  }

  .login-wrapper {
    max-width: 100%;
  }

  .apple-card {
    margin: 0;
    border-radius: var(--radius-md);
  }

  .apple-header {
    padding: var(--space-5) var(--space-4);
  }

  .apple-body {
    padding: var(--space-4);
  }

  .apple-title {
    font-size: var(--text-xl);
  }

  .apple-options {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3);
  }

  .footer-links {
    flex-direction: column;
    gap: var(--space-3);
  }

  .apple-input {
    font-size: 16px; /* 防止iOS缩放 */
  }
}

/* 小屏幕 (手机横屏/小平板) */
@media (min-width: 481px) and (max-width: 768px) {
  .login-container {
    padding: var(--space-6);
  }

  .login-wrapper {
    max-width: 400px;
  }

  .apple-header {
    padding: var(--space-6) var(--space-5);
  }

  .apple-body {
    padding: var(--space-5);
  }
}

/* 中等屏幕 (平板) */
@media (min-width: 769px) and (max-width: 1024px) {
  .login-wrapper {
    max-width: 420px;
  }
}

/* 大屏幕 (桌面) */
@media (min-width: 1025px) {
  .login-wrapper {
    max-width: 400px;
  }
}

/* 超宽屏幕 */
@media (min-width: 1440px) {
  .login-wrapper {
    max-width: 420px;
  }
}

/* 横屏手机特殊处理 */
@media (max-height: 600px) and (orientation: landscape) {
  .apple-header {
    padding: var(--space-4) var(--space-6);
  }

  .apple-body {
    padding: var(--space-4) var(--space-6);
  }

  .apple-footer {
    margin-top: var(--space-6);
    padding: var(--space-4) 0;
  }
}

/* ===================================
   Apple可访问性和兼容性
   =================================== */

/* 高对比度模式 */
@media (prefers-contrast: high) {
  :root {
    --primary: #0000ff;
    --border-medium: #000000;
    --text-primary: #000000;
    --text-secondary: #333333;
  }

  .apple-card {
    border: 2px solid var(--border-medium);
  }

  .apple-input {
    border-width: 2px;
  }

  .apple-button {
    border: 2px solid transparent;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* 焦点可见性 */
.apple-input:focus-visible,
.apple-button:focus-visible,
.apple-checkbox-input:focus-visible {
  outline: 2px solid var(--primary-blue);
  outline-offset: 2px;
}

/* 键盘导航支持 */
.apple-button:focus,
.apple-input:focus,
.apple-input-action:focus {
  z-index: 1;
}

/* ===================================
   Apple打印样式
   =================================== */

@media print {
  body {
    background: white !important;
    color: black !important;
  }

  .apple-card {
    background: white !important;
    box-shadow: none !important;
    border: 1px solid #000 !important;
  }

  .apple-button-primary {
    background: white !important;
    color: black !important;
    border: 1px solid black !important;
  }

  .apple-input {
    background: white !important;
    border: 1px solid #000 !important;
    color: black !important;
  }
}




