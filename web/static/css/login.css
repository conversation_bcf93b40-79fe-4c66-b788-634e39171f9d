/* ===================================
   Login Page Styles - Bootstrap 5.3.7
   使用CSS变量系统的现代化登录页面样式
   =================================== */

/* CSS变量定义 */
:root {
  /* 主色调 */
  --login-primary: #667eea;
  --login-secondary: #764ba2;
  --login-gradient: linear-gradient(135deg, var(--login-primary) 0%, var(--login-secondary) 100%);

  /* 卡片样式 */
  --login-card-radius: 1rem;
  --login-card-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  --login-card-backdrop: blur(20px);

  /* 动画时间 */
  --login-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --login-animation-duration: 0.6s;

  /* 间距 */
  --login-spacing-xs: 0.5rem;
  --login-spacing-sm: 1rem;
  --login-spacing-md: 1.5rem;
  --login-spacing-lg: 2rem;
  --login-spacing-xl: 3rem;
}

/* 暗色主题变量 */
[data-bs-theme="dark"] {
  --login-card-bg: rgba(33, 37, 41, 0.95);
  --login-card-border: rgba(255, 255, 255, 0.125);
  --login-text-muted: rgba(255, 255, 255, 0.6);
}

/* 基础样式 */
body {
  font-family: var(--bs-font-sans-serif);
  background: var(--login-gradient);
  position: relative;
  overflow-x: hidden;
}

/* 背景图案 */
.login-bg-pattern {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

/* 登录卡片 */
.login-card {
  border-radius: var(--login-card-radius);
  backdrop-filter: var(--login-card-backdrop);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  animation: fadeInUp var(--login-animation-duration) ease-out;
  position: relative;
  z-index: 1;
}

[data-bs-theme="dark"] .login-card {
  background: var(--login-card-bg);
  border-color: var(--login-card-border);
}

/* 卡片头部 */
.login-header {
  background: var(--login-gradient);
  color: white;
  padding: var(--login-spacing-lg);
  position: relative;
}

.login-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  opacity: 0.3;
}

.login-icon {
  font-size: 3rem;
  opacity: 0.9;
}

/* 卡片主体 */
.login-body {
  background: transparent;
}

/* 表单控件 */
.form-floating > .form-control {
  border-radius: var(--bs-border-radius-lg);
  border-width: 2px;
  padding: 1rem 0.75rem;
  transition: var(--login-transition);
}

.form-floating > .form-control:focus {
  border-color: var(--login-primary);
  box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.15);
  transform: translateY(-1px);
}

.form-floating > label {
  padding: 1rem 0.75rem;
  color: var(--bs-secondary-color);
  transition: var(--login-transition);
}

/* 登录按钮 */
.btn-login {
  background: var(--login-gradient);
  border: none;
  border-radius: var(--bs-border-radius-lg);
  font-weight: 600;
  letter-spacing: 0.025em;
  transition: var(--login-transition);
  position: relative;
  overflow: hidden;
}

.btn-login::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-login:hover::before {
  left: 100%;
}

.btn-login:hover {
  transform: translateY(-2px);
  box-shadow: 0 0.5rem 1rem rgba(102, 126, 234, 0.3);
}

.btn-login:active {
  transform: translateY(0);
}

/* 按钮加载状态 */
.btn-login .btn-spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 复选框样式 */
.form-check-input {
  border-radius: 0.375rem;
  border-width: 2px;
  transition: var(--login-transition);
}

.form-check-input:checked {
  background-color: var(--login-primary);
  border-color: var(--login-primary);
}

.form-check-input:focus {
  box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.15);
}

/* 链接样式 */
a {
  color: var(--login-primary);
  transition: var(--login-transition);
}

a:hover {
  color: var(--login-secondary);
  text-decoration: underline !important;
}

/* 警告框样式 */
.alert {
  border-radius: var(--bs-border-radius-lg);
  border: none;
  animation: slideDown 0.3s ease-out;
}

/* 卡片底部 */
.login-footer {
  background: var(--bs-tertiary-bg);
  padding: var(--login-spacing-sm);
  font-size: 0.875rem;
}

[data-bs-theme="dark"] .login-footer {
  background: rgba(255, 255, 255, 0.05);
}

/* 主题切换按钮 */
#themeToggle {
  border-radius: 2rem;
  transition: var(--login-transition);
}

#themeToggle:hover {
  transform: scale(1.05);
}

/* ===================================
   动画效果
   =================================== */

/* 卡片入场动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(2rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 警告框滑入动画 */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-0.5rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 脉冲动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* 加载动画 */
.loading {
  animation: pulse 1.5s ease-in-out infinite;
}

/* ===================================
   响应式设计
   =================================== */

/* 超小屏幕 (手机) */
@media (max-width: 575.98px) {
  .login-card {
    margin: var(--login-spacing-sm);
    border-radius: var(--bs-border-radius);
  }

  .login-header {
    padding: var(--login-spacing-md);
  }

  .login-icon {
    font-size: 2.5rem;
  }

  .card-body {
    padding: var(--login-spacing-md) !important;
  }

  .login-footer {
    padding: var(--login-spacing-xs);
    font-size: 0.8rem;
  }

  .login-footer .row > div {
    text-align: center;
    margin-bottom: 0.25rem;
  }
}

/* 小屏幕 (平板) */
@media (min-width: 576px) and (max-width: 767.98px) {
  .login-card {
    max-width: 400px;
    margin: 0 auto;
  }
}

/* 中等屏幕 (小型笔记本) */
@media (min-width: 768px) and (max-width: 991.98px) {
  .login-card {
    max-width: 450px;
    margin: 0 auto;
  }
}

/* 大屏幕 (桌面) */
@media (min-width: 992px) {
  .login-card {
    max-width: 400px;
  }

  .login-header {
    padding: var(--login-spacing-xl);
  }
}

/* ===================================
   表单验证样式
   =================================== */

.was-validated .form-control:valid,
.form-control.is-valid {
  border-color: var(--bs-success);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='M2.3 6.73.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1'/%3e%3c/svg%3e");
}

.was-validated .form-control:invalid,
.form-control.is-invalid {
  border-color: var(--bs-danger);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 2.4 2.4m0-2.4L5.8 7'/%3e%3c/svg%3e");
}

/* ===================================
   辅助功能和可访问性
   =================================== */

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .login-card {
    border: 2px solid var(--bs-border-color);
  }

  .form-control {
    border-width: 2px;
  }

  .btn-login {
    border: 2px solid var(--login-primary);
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .login-card {
    animation: none;
  }

  .btn-login:hover {
    transform: none;
  }
}

/* 焦点可见性 */
.form-control:focus-visible,
.btn:focus-visible,
.form-check-input:focus-visible {
  outline: 2px solid var(--login-primary);
  outline-offset: 2px;
}

/* ===================================
   打印样式
   =================================== */

@media print {
  .login-bg-pattern,
  #themeToggle {
    display: none !important;
  }

  .login-card {
    box-shadow: none !important;
    border: 1px solid #000 !important;
  }

  .login-header {
    background: #f8f9fa !important;
    color: #000 !important;
  }
}
