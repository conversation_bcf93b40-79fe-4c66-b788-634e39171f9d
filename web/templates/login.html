<!DOCTYPE html>
<html lang="zh-CN" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 蜜罐管理平台</title>

    <!-- Bootstrap CSS -->
    <link href="/static/libs/bootstrap/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="/static/libs/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom Login CSS -->
    <link href="/static/css/login.css" rel="stylesheet">
</head>
<body>
    <!-- Background Pattern -->
    <div class="login-bg-pattern"></div>

    <!-- Main Login Container -->
    <div class="container-fluid vh-100 d-flex align-items-center justify-content-center">
        <div class="row w-100 justify-content-center">
            <div class="col-12 col-sm-8 col-md-6 col-lg-4 col-xl-3">
                <!-- Login Card -->
                <div class="card login-card shadow-lg border-0">
                    <!-- Card Header -->
                    <div class="card-header login-header text-center border-0">
                        <div class="login-icon mb-3">
                            <i class="bi bi-shield-check"></i>
                        </div>
                        <h1 class="h4 mb-2 fw-normal">蜜罐管理平台</h1>
                        <p class="text-white-50 mb-0 small">Honeypot Management System</p>
                    </div>

                    <!-- Card Body -->
                    <div class="card-body login-body p-4">
                        <!-- Alert Messages -->
                        {{if .ErrorMessage}}
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>{{.ErrorMessage}}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        {{end}}

                        {{if .InfoMessage}}
                        <div class="alert alert-info alert-dismissible fade show" role="alert">
                            <i class="bi bi-info-circle-fill me-2"></i>{{.InfoMessage}}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        {{end}}

                        <!-- Login Form -->
                        <form id="loginForm" method="POST" action="/login" novalidate>
                            <!-- Username Field -->
                            <div class="form-floating mb-3">
                                <input type="text"
                                       class="form-control"
                                       id="username"
                                       name="username"
                                       placeholder="用户名"
                                       required
                                       autocomplete="username">
                                <label for="username">
                                    <i class="bi bi-person me-2"></i>用户名
                                </label>
                                <div class="invalid-feedback">
                                    请输入用户名
                                </div>
                            </div>

                            <!-- Password Field -->
                            <div class="form-floating mb-3">
                                <input type="password"
                                       class="form-control"
                                       id="password"
                                       name="password"
                                       placeholder="密码"
                                       required
                                       autocomplete="current-password">
                                <label for="password">
                                    <i class="bi bi-lock me-2"></i>密码
                                </label>
                                <div class="invalid-feedback">
                                    请输入密码
                                </div>
                            </div>

                            <!-- Remember Me & Forgot Password -->
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="remember" name="remember">
                                    <label class="form-check-label small" for="remember">
                                        记住我
                                    </label>
                                </div>
                                <a href="/forgot-password" class="text-decoration-none small">
                                    忘记密码？
                                </a>
                            </div>

                            <!-- Submit Button -->
                            <button type="submit" class="btn btn-primary btn-login w-100 py-3 mb-3">
                                <i class="bi bi-box-arrow-in-right me-2"></i>
                                <span class="btn-text">登录</span>
                                <span class="btn-spinner d-none">
                                    <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                    登录中...
                                </span>
                            </button>
                        </form>
                    </div>

                    <!-- Card Footer -->
                    <div class="card-footer login-footer border-0 text-center">
                        <div class="row g-0 small text-muted">
                            <div class="col-6">
                                <i class="bi bi-server me-1"></i>
                                系统状态: <span class="text-success">正常</span>
                            </div>
                            <div class="col-6">
                                <i class="bi bi-clock me-1"></i>
                                {{.CurrentTime}}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Theme Toggle -->
                <div class="text-center mt-3">
                    <button type="button" class="btn btn-outline-light btn-sm" id="themeToggle">
                        <i class="bi bi-moon-stars me-1"></i>
                        <span class="theme-text">深色模式</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="/static/libs/bootstrap/bootstrap.bundle.min.js"></script>

    <script>
        // ===================================
        // 登录页面主要功能
        // ===================================

        class LoginManager {
            constructor() {
                this.form = document.getElementById('loginForm');
                this.submitBtn = this.form.querySelector('button[type="submit"]');
                this.btnText = this.submitBtn.querySelector('.btn-text');
                this.btnSpinner = this.submitBtn.querySelector('.btn-spinner');
                this.themeToggle = document.getElementById('themeToggle');

                this.init();
            }

            init() {
                this.bindEvents();
                this.checkExistingLogin();
                this.initTheme();
                this.focusFirstInput();
            }

            bindEvents() {
                // 表单提交事件
                this.form.addEventListener('submit', (e) => this.handleSubmit(e));

                // 主题切换事件
                this.themeToggle.addEventListener('click', () => this.toggleTheme());

                // 实时表单验证
                this.form.querySelectorAll('.form-control').forEach(input => {
                    input.addEventListener('blur', () => this.validateField(input));
                    input.addEventListener('input', () => this.clearValidation(input));
                });

                // 回车键快捷登录
                document.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !this.submitBtn.disabled) {
                        this.form.dispatchEvent(new Event('submit'));
                    }
                });
            }

            async handleSubmit(e) {
                e.preventDefault();

                // 表单验证
                if (!this.validateForm()) {
                    return;
                }

                const formData = new FormData(this.form);
                const loginData = {
                    username: formData.get('username'),
                    password: formData.get('password'),
                    remember: formData.get('remember') === 'on'
                };

                try {
                    this.setLoadingState(true);
                    await this.performLogin(loginData);
                } catch (error) {
                    this.handleLoginError(error);
                } finally {
                    this.setLoadingState(false);
                }
            }

            validateForm() {
                let isValid = true;
                const inputs = this.form.querySelectorAll('.form-control[required]');

                inputs.forEach(input => {
                    if (!this.validateField(input)) {
                        isValid = false;
                    }
                });

                return isValid;
            }

            validateField(input) {
                const value = input.value.trim();
                const isValid = value.length > 0;

                input.classList.remove('is-valid', 'is-invalid');
                input.classList.add(isValid ? 'is-valid' : 'is-invalid');

                return isValid;
            }

            clearValidation(input) {
                input.classList.remove('is-valid', 'is-invalid');
            }

            async performLogin(loginData) {
                const response = await fetch('/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(loginData)
                });

                const data = await response.json();

                if (data.success) {
                    this.handleLoginSuccess(data);
                } else {
                    throw new Error(data.message || '登录失败');
                }
            }

            handleLoginSuccess(data) {
                // 保存认证信息
                localStorage.setItem('token', data.data.token);
                localStorage.setItem('user', JSON.stringify(data.data.user_info));

                this.showAlert('登录成功，正在跳转...', 'success');

                // 延迟跳转，让用户看到成功提示
                setTimeout(() => {
                    window.location.href = '/dashboard';
                }, 1500);
            }

            handleLoginError(error) {
                console.error('Login error:', error);
                this.showAlert(error.message || '网络错误，请稍后重试', 'danger');
            }

            setLoadingState(loading) {
                this.submitBtn.disabled = loading;

                if (loading) {
                    this.btnText.classList.add('d-none');
                    this.btnSpinner.classList.remove('d-none');
                } else {
                    this.btnText.classList.remove('d-none');
                    this.btnSpinner.classList.add('d-none');
                }
            }

            showAlert(message, type) {
                // 移除现有警告
                const existingAlerts = document.querySelectorAll('.alert');
                existingAlerts.forEach(alert => alert.remove());

                // 创建新警告
                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
                alertDiv.innerHTML = `
                    <i class="bi bi-${type === 'success' ? 'check-circle-fill' : 'exclamation-triangle-fill'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                `;

                // 插入警告
                const cardBody = document.querySelector('.login-body');
                cardBody.insertBefore(alertDiv, this.form);
            }

            checkExistingLogin() {
                if (localStorage.getItem('token')) {
                    window.location.href = '/dashboard';
                }
            }

            initTheme() {
                const savedTheme = localStorage.getItem('theme') || 'light';
                this.setTheme(savedTheme);
            }

            toggleTheme() {
                const currentTheme = document.documentElement.getAttribute('data-bs-theme');
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                this.setTheme(newTheme);
            }

            setTheme(theme) {
                document.documentElement.setAttribute('data-bs-theme', theme);
                localStorage.setItem('theme', theme);

                const icon = this.themeToggle.querySelector('i');
                const text = this.themeToggle.querySelector('.theme-text');

                if (theme === 'dark') {
                    icon.className = 'bi bi-sun me-1';
                    text.textContent = '浅色模式';
                } else {
                    icon.className = 'bi bi-moon-stars me-1';
                    text.textContent = '深色模式';
                }
            }

            focusFirstInput() {
                // 页面加载完成后聚焦第一个输入框
                setTimeout(() => {
                    const firstInput = this.form.querySelector('.form-control');
                    if (firstInput) {
                        firstInput.focus();
                    }
                }, 100);
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            new LoginManager();
        });
    </script>
</body>
</html>
