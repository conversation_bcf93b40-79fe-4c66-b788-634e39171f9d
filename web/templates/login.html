<!DOCTYPE html>
<html lang="zh-CN" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 蜜罐管理平台</title>

    <!-- Bootstrap CSS -->
    <link href="/static/libs/bootstrap/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="/static/libs/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">

    <!-- Apple System Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">

    <!-- Custom Login CSS -->
    <link href="/static/css/login.css" rel="stylesheet">
</head>
<body>
    <!-- Apple Clean Background -->
    <div class="apple-background"></div>

    <!-- Main Login Container -->
    <div class="login-container">
        <div class="login-wrapper">
            <!-- Apple Login Card -->
                <div class="apple-card">
                    <!-- Apple Header -->
                    <div class="apple-header">
                        <div class="apple-logo">
                            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                                <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
                            </svg>
                        </div>
                        <h1 class="apple-title">蜜罐管理平台</h1>
                        <p class="apple-subtitle">使用您的账户登录</p>
                    </div>

                    <!-- Apple Card Body -->
                    <div class="apple-body">
                        <!-- Apple Alert Messages -->
                        {{if .ErrorMessage}}
                        <div class="apple-alert apple-alert-error" role="alert">
                            <span>{{.ErrorMessage}}</span>
                        </div>
                        {{end}}

                        {{if .InfoMessage}}
                        <div class="apple-alert apple-alert-info" role="alert">
                            <span>{{.InfoMessage}}</span>
                        </div>
                        {{end}}

                        <!-- Apple Login Form -->
                        <form id="loginForm" method="POST" action="/login" novalidate>
                            <!-- Apple Username Field -->
                            <div class="apple-field">
                                <label for="username" class="apple-label">用户名</label>
                                <input type="text"
                                       class="apple-input"
                                       id="username"
                                       name="username"
                                       required
                                       autocomplete="username">
                            </div>

                            <!-- Apple Password Field -->
                            <div class="apple-field">
                                <label for="password" class="apple-label">密码</label>
                                <div class="apple-input-wrapper">
                                    <input type="password"
                                           class="apple-input"
                                           id="password"
                                           name="password"
                                           required
                                           autocomplete="current-password">
                                    <button type="button" class="apple-input-action password-toggle">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" id="passwordToggleIcon">
                                            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                                            <circle cx="12" cy="12" r="3"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>

                            <!-- Apple Options -->
                            <div class="apple-options">
                                <div class="apple-checkbox">
                                    <input type="checkbox" id="remember" name="remember" class="apple-checkbox-input">
                                    <label for="remember" class="apple-checkbox-label">记住我</label>
                                </div>
                                <a href="/forgot-password" class="apple-link">忘记密码？</a>
                            </div>

                            <!-- Apple Submit Button -->
                            <div class="apple-button-group">
                                <button type="submit" class="apple-button apple-button-primary">
                                    <span class="button-content">登录</span>
                                    <span class="button-loading d-none">
                                        <span class="loading-spinner"></span>
                                        <span>验证中...</span>
                                    </span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Apple Footer -->
                <div class="apple-footer">
                    <div class="footer-links">
                        <a href="/help" class="footer-link">帮助</a>
                        <a href="/privacy" class="footer-link">隐私政策</a>
                        <a href="/terms" class="footer-link">服务条款</a>
                    </div>
                    <div class="apple-copyright">
                        <span>© 2024 蜜罐管理平台. All rights reserved.</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="/static/libs/bootstrap/bootstrap.bundle.min.js"></script>

    <script>
        // ===================================
        // Apple极简风格登录页面管理器
        // ===================================

        class AppleLoginManager {
            constructor() {
                this.form = document.getElementById('loginForm');
                this.submitBtn = this.form.querySelector('.apple-button-primary');
                this.btnContent = this.submitBtn.querySelector('.button-content');
                this.btnSpinner = this.submitBtn.querySelector('.button-loading');
                this.passwordToggle = document.querySelector('.password-toggle');

                this.isLoading = false;

                this.init();
            }

            init() {
                this.bindEvents();
                this.checkExistingLogin();
                this.initPasswordToggle();
                this.focusFirstInput();
            }

            bindEvents() {
                // 表单提交事件
                this.form.addEventListener('submit', (e) => this.handleSubmit(e));

                // 密码显示/隐藏切换
                if (this.passwordToggle) {
                    this.passwordToggle.addEventListener('click', () => this.togglePasswordVisibility());
                }

                // 实时表单验证
                this.form.querySelectorAll('.apple-input').forEach(input => {
                    input.addEventListener('blur', () => this.validateField(input));
                    input.addEventListener('input', () => this.clearValidation(input));
                });

                // 回车键快捷登录
                document.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !this.isLoading) {
                        e.preventDefault();
                        this.form.dispatchEvent(new Event('submit'));
                    }
                });
            }

            async handleSubmit(e) {
                e.preventDefault();

                // 表单验证
                if (!this.validateForm()) {
                    return;
                }

                const formData = new FormData(this.form);
                const loginData = {
                    username: formData.get('username'),
                    password: formData.get('password'),
                    remember: formData.get('remember') === 'on'
                };

                try {
                    this.setLoadingState(true);
                    await this.performLogin(loginData);
                } catch (error) {
                    this.handleLoginError(error);
                } finally {
                    this.setLoadingState(false);
                }
            }

            validateForm() {
                let isValid = true;
                const inputs = this.form.querySelectorAll('.form-control[required]');

                inputs.forEach(input => {
                    if (!this.validateField(input)) {
                        isValid = false;
                    }
                });

                return isValid;
            }

            validateField(input) {
                const value = input.value.trim();
                const isValid = value.length > 0;

                input.classList.remove('is-valid', 'is-invalid');
                input.classList.add(isValid ? 'is-valid' : 'is-invalid');

                return isValid;
            }

            clearValidation(input) {
                input.classList.remove('is-valid', 'is-invalid');
            }

            async performLogin(loginData) {
                const response = await fetch('/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(loginData)
                });

                const data = await response.json();

                if (data.success) {
                    this.handleLoginSuccess(data);
                } else {
                    throw new Error(data.message || '登录失败');
                }
            }

            handleLoginSuccess(data) {
                // 保存认证信息
                localStorage.setItem('token', data.data.token);
                localStorage.setItem('user', JSON.stringify(data.data.user_info));

                this.showAlert('登录成功，正在跳转...', 'success');

                // 延迟跳转，让用户看到成功提示
                setTimeout(() => {
                    window.location.href = '/dashboard';
                }, 1500);
            }

            handleLoginError(error) {
                console.error('Login error:', error);
                this.showAlert(error.message || '网络错误，请稍后重试', 'danger');
            }

            setLoadingState(loading) {
                this.isLoading = loading;
                this.submitBtn.disabled = loading;

                if (loading) {
                    this.btnContent.style.opacity = '0';
                    this.btnSpinner.classList.remove('d-none');
                } else {
                    this.btnContent.style.opacity = '1';
                    this.btnSpinner.classList.add('d-none');
                }
            }

            initPasswordToggle() {
                if (this.passwordToggle) {
                    this.passwordToggle.addEventListener('click', () => {
                        this.togglePasswordVisibility();
                    });
                }
            }

            togglePasswordVisibility() {
                const passwordInput = document.getElementById('password');
                const toggleIcon = document.getElementById('passwordToggleIcon');

                if (passwordInput.type === 'password') {
                    passwordInput.type = 'text';
                    toggleIcon.innerHTML = '<path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/><line x1="1" y1="1" x2="23" y2="23"/>';
                } else {
                    passwordInput.type = 'password';
                    toggleIcon.innerHTML = '<path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/><circle cx="12" cy="12" r="3"/>';
                }
            }

            updateTime() {
                // 更新时间显示
                const timeDisplay = document.getElementById('currentTime');
                if (timeDisplay) {
                    const now = new Date();
                    timeDisplay.textContent = now.toLocaleTimeString('zh-CN');

                    // 每分钟更新一次
                    setInterval(() => {
                        const now = new Date();
                        timeDisplay.textContent = now.toLocaleTimeString('zh-CN');
                    }, 60000);
                }
            }

            showAlert(message, type) {
                // 移除现有警告
                const existingAlerts = document.querySelectorAll('.alert');
                existingAlerts.forEach(alert => alert.remove());

                // 创建新警告
                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
                alertDiv.innerHTML = `
                    <i class="bi bi-${type === 'success' ? 'check-circle-fill' : 'exclamation-triangle-fill'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                `;

                // 插入警告
                const cardBody = document.querySelector('.login-body');
                cardBody.insertBefore(alertDiv, this.form);
            }

            checkExistingLogin() {
                if (localStorage.getItem('token')) {
                    window.location.href = '/dashboard';
                }
            }

            initTheme() {
                const savedTheme = localStorage.getItem('theme') || 'light';
                this.setTheme(savedTheme);
            }

            toggleTheme() {
                const currentTheme = document.documentElement.getAttribute('data-bs-theme');
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                this.setTheme(newTheme);
            }

            // 移除主题切换功能，Apple风格使用系统主题

            focusFirstInput() {
                // 页面加载完成后聚焦第一个输入框
                setTimeout(() => {
                    const firstInput = this.form.querySelector('.form-control');
                    if (firstInput) {
                        firstInput.focus();
                    }
                }, 100);
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            new AppleLoginManager();
        });
    </script>
</body>
</html>
